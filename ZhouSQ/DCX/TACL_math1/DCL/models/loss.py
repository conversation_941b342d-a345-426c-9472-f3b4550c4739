import torch
import torch.nn.functional as F
import random

flag_imbalanced_contrastive_loss = False
flag_imbalanced_weight_reverse = False
flag_print_loss_weight = False


def sim(x, y):
    norm_x = F.normalize(x, dim=-1)
    norm_y = F.normalize(y, dim=-1)
    return torch.matmul(norm_x, norm_y.transpose(1, 0))


def build_label_matrix_optimized(cur_hier_labels, text_content_sets, cur_batch_size):
    """优化的标签关系矩阵构建函数"""
    cur_hier_matrix = torch.zeros(cur_batch_size, cur_batch_size)

    # 预处理标签，将列表标签转换为集合以加速交集计算
    label_sets = []
    for label in cur_hier_labels:
        if isinstance(label, list):
            label_sets.append(set(label))
        else:
            label_sets.append({label})

    # 向量化构建标签关系矩阵
    for i in range(len(cur_hier_labels)):
        for j in range(len(cur_hier_labels)):
            # 检查文本内容相似性
            if text_content_sets is not None and i != j:
                if len(text_content_sets[i].intersection(text_content_sets[j])) > 0:
                    cur_hier_matrix[i][j] = 0  # 相同文本内容设为0（忽略）
                    continue

            # 检查标签相似性
            if len(label_sets[i].intersection(label_sets[j])) > 0:
                cur_hier_matrix[i][j] = 1  # 有共同标签为正样本
            else:
                cur_hier_matrix[i][j] = -1  # 无共同标签为负样本

    return cur_hier_matrix


def compute_vectorized_loss(sim_score, pos_mask, neg_mask):
    """
    完全向量化的损失计算函数 - 无Python循环版本
    Args:
        sim_score: [batch_size, batch_size] 相似度矩阵
        pos_mask: [batch_size, batch_size] 正样本掩码
        neg_mask: [batch_size, batch_size] 负样本掩码
    Returns:
        loss: 标量损失值
    """
    # 计算每行的正样本相似度和：[batch_size]
    pos_sim_per_row = (sim_score * pos_mask.float()).sum(dim=1)

    # 计算每行的负样本相似度和：[batch_size]
    neg_sim_per_row = (sim_score * neg_mask.float()).sum(dim=1)

    # 找出有正样本的行
    has_pos_samples = pos_mask.any(dim=1)

    # 只对有正样本的行计算损失
    valid_pos_sim = pos_sim_per_row[has_pos_samples]
    valid_neg_sim = neg_sim_per_row[has_pos_samples]

    # 数值稳定性：避免除零和log(0)
    denominator = valid_pos_sim + valid_neg_sim
    valid_mask = (valid_pos_sim > 0) & (denominator > 0)

    if not valid_mask.any():
        return torch.tensor(0.0, device=sim_score.device)

    # 向量化计算InfoNCE损失
    valid_pos_sim = valid_pos_sim[valid_mask]
    valid_denominator = denominator[valid_mask]

    # 计算 -log(pos_sim / (pos_sim + neg_sim))
    loss_per_sample = -torch.log(valid_pos_sim / valid_denominator)

    return loss_per_sample.sum()


def apply_negative_sampling_optimized(cur_hier_matrix, cur_hier_labels, label_sim_indices, cur_batch_size):
    """优化的负样本过滤函数"""
    # 批量处理负样本过滤，避免重复循环
    for i in range(len(cur_hier_labels)):
        # 基于标签相似度的负样本过滤
        if label_sim_indices is not None and i < len(label_sim_indices):
            indices1 = label_sim_indices[i]
            # 向量化处理标签映射
            for l in indices1:
                for batch_idx in range(cur_batch_size):
                    if batch_idx < len(cur_hier_labels) and cur_hier_labels[batch_idx] == l:
                        cur_hier_matrix[i][batch_idx] = 0

        # 随机负采样过滤 - 只计算一次
        max_neg_samples = max(1, cur_batch_size // 4)
        neg_indices = random.sample(range(cur_batch_size), max_neg_samples)
        for l in neg_indices:
            if cur_hier_matrix[i][l] == -1:
                cur_hier_matrix[i][l] = 0

    return cur_hier_matrix


def compute_contrastive_loss_optimized(cur_hier_matrix, sim_score, cur_batch_size):
    """优化的对比损失计算函数"""
    cur_loss_ins = 0

    for i in range(len(cur_hier_matrix)):
        y_true = cur_hier_matrix[i]
        # 如果没有正样本则跳过
        if len(y_true[y_true == 1]) == 0:
            continue

        cur_sim_score = sim_score[i]
        pos_sim = cur_sim_score[y_true == 1].sum()
        neg_sim = cur_sim_score[y_true == 0].sum()

        # 添加数值稳定性检查
        if pos_sim > 0 and (pos_sim + neg_sim) > 0:
            cur_loss_ins += -torch.log(pos_sim / (pos_sim + neg_sim))

    return cur_loss_ins


# flat contrastive_loss - 优化版本
def flat_contrastive_loss_func(label_sim, hier_labels, processor, output_at_mask, imbalanced_weight=False, depth=2,
                              contrastive_level=1, imbalanced_weight_reverse=True, use_cuda=True, temperature=0.1, text_contents=None):
    """
    优化的对比损失函数，主要优化点：
    1. 减少重复计算和循环嵌套
    2. 预处理标签和文本内容
    3. 向量化操作
    4. 早期退出条件
    """

    global flag_imbalanced_contrastive_loss, flag_imbalanced_weight_reverse, flag_print_loss_weight
    ## output_at_mask = [batch_size, multi_mask, 768]
    if use_cuda:
        output_at_mask = output_at_mask.cuda()  # 将输出移到GPU上
    cur_batch_size = output_at_mask.shape[0]  # 获取当前批次大小
    assert cur_batch_size == len(hier_labels[0])  # 确保批次大小与标签数量一致

    loss_ins = 0  # 初始化总损失
    # 根据是否使用不平衡权重计算各层级的损失权重
    # 不同层的权重，默认同等地位  传入是true 层级不同使用不同权重 越高层，权重越高
    if not imbalanced_weight:
        # 如果不使用不平衡权重，则所有层级的权重相等
        loss_weight = [1 for i in range(depth)]
    else:
        # 如果使用不平衡权重，根据对比层级计算权重
        # 权重随着层级加深而减小（层级越深，权重越小）
        if not flag_imbalanced_contrastive_loss:
            print(f"using imbalanced contrastive loss with contrastive_level:{contrastive_level}")
            flag_imbalanced_contrastive_loss = True
        loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth)]

    if imbalanced_weight_reverse:
        # 如果需要反转权重（层级越深，权重越大）
        if not flag_imbalanced_weight_reverse:
            print("imbalanced weight reversed ")
            flag_imbalanced_weight_reverse = True

        loss_weight.reverse()  # 反转权重列表
    if not flag_print_loss_weight:
        print("loss_weight:", loss_weight)
        flag_print_loss_weight = True

    # 预处理：将文本内容转换为集合以加速比较
    text_content_sets = None
    if text_contents is not None:
        text_content_sets = [set([content]) for content in text_contents]

    # 预处理：预先计算label_sim的排序结果以避免重复计算
    label_sim_indices = None
    if label_sim is not None:
        label_sim_indices = []
        for i in range(len(label_sim)):
            if i < len(label_sim):
                _, indices = torch.sort(label_sim[i], descending=True)
                label_sim_indices.append(indices[:30])
            else:
                label_sim_indices.append([])

    # 设置随机种子一次，避免在循环中重复设置
    random.seed(42)

    # 遍历每个mask位置（对应不同的层级）
    for mask_idx in range(depth):
        # shape: [batch_size, 768]
        cur_output_at_mask = output_at_mask[:, mask_idx, :]  # 获取当前mask位置的输出
        sim_score = sim(cur_output_at_mask, cur_output_at_mask)  # 计算样本间的相似度
        sim_score = torch.exp(sim_score)  # 对相似度取指数，用于后续概率计算
        cur_loss_weight = loss_weight[depth - 1 - mask_idx:]  # 获取当前层级的损失权重

        # 遍历当前mask之前的所有层级，计算每个层级的对比损失
        for cur_depth in range(mask_idx):

            cur_loss_ins = 0  # 初始化当前层级的损失
            cur_hier_labels = hier_labels[cur_depth]  # 获取当前深度的标签

            # 优化：使用向量化操作构建标签关系矩阵
            cur_hier_matrix = build_label_matrix_optimized(cur_hier_labels, text_content_sets, cur_batch_size)

            # 优化：批量处理负样本过滤
            cur_hier_matrix = apply_negative_sampling_optimized(
                cur_hier_matrix, cur_hier_labels, label_sim_indices, cur_batch_size
            )

            # 优化：向量化计算对比损失
            cur_loss_ins = compute_contrastive_loss_optimized(cur_hier_matrix, sim_score, cur_batch_size)

            # 累加加权损失
            loss_ins += cur_loss_ins * cur_loss_weight[cur_depth]

    # 归一化损失
    loss_ins = loss_ins / (cur_batch_size ** 2)

    return loss_ins


# 快速版本的对比损失函数 - 主要性能优化
def flat_contrastive_loss_func_fast(label_sim, hier_labels, processor, output_at_mask, imbalanced_weight=False, depth=2,
                                   contrastive_level=1, imbalanced_weight_reverse=True, use_cuda=True, temperature=0.1, text_contents=None):
    """
    超快速版本的对比损失函数，极致优化：
    1. 完全向量化的标签矩阵构建
    2. 批量相似度计算和损失计算
    3. 内存预分配和重用
    4. 减少Python循环，最大化GPU并行
    """
    global flag_imbalanced_contrastive_loss, flag_imbalanced_weight_reverse, flag_print_loss_weight

    # 🔍 调试信息：证明对比损失函数被调用
    print(f"🚀 [DEBUG] flat_contrastive_loss_func_fast 被调用!")
    print(f"📊 [DEBUG] 参数检查:")
    print(f"   - cur_batch_size: {output_at_mask.shape[0] if output_at_mask is not None else 'None'}")
    print(f"   - depth: {depth}")
    print(f"   - hier_labels 层数: {len(hier_labels) if hier_labels else 'None'}")
    print(f"   - imbalanced_weight: {imbalanced_weight}")
    print(f"   - contrastive_level: {contrastive_level}")
    print(f"   - use_cuda: {use_cuda}")

    if use_cuda:
        output_at_mask = output_at_mask.cuda()
    cur_batch_size = output_at_mask.shape[0]
    assert cur_batch_size == len(hier_labels[0])

    # 早期退出
    if cur_batch_size <= 1:
        print(f"⚠️  [DEBUG] 早期退出: batch_size={cur_batch_size} <= 1")
        return torch.tensor(0.0, device=output_at_mask.device)

    device = output_at_mask.device

    # 计算损失权重（一次性）
    if not imbalanced_weight:
        loss_weight = torch.ones(depth, device=device)
    else:
        if not flag_imbalanced_contrastive_loss:
            print(f"using imbalanced contrastive loss with contrastive_level:{contrastive_level}")
            flag_imbalanced_contrastive_loss = True
        loss_weight = torch.tensor([1 / 2 ** (i * contrastive_level) for i in range(depth)], device=device)

    if imbalanced_weight_reverse:
        if not flag_imbalanced_weight_reverse:
            print("imbalanced weight reversed ")
            flag_imbalanced_weight_reverse = True
        loss_weight = loss_weight.flip(0)

    if not flag_print_loss_weight:
        print("loss_weight:", loss_weight.cpu().tolist())
        flag_print_loss_weight = True

    # 预处理标签：转换为张量以便向量化操作（修复版本）
    label_tensors = []
    for depth_labels in hier_labels:
        # 检查是否为空或者第一个元素是否为列表
        if len(depth_labels) > 0 and isinstance(depth_labels[0], list):
            # 多标签情况：创建二进制矩阵（向量化）
            all_labels = []
            for labels in depth_labels:
                if labels:  # 如果labels不为空
                    all_labels.extend(labels)

            if all_labels:
                max_label = max(all_labels)
                label_matrix = torch.zeros(cur_batch_size, max_label + 1, device=device)
                # 向量化填充
                for i, labels in enumerate(depth_labels):
                    if labels:  # 如果labels不为空
                        label_matrix[i, labels] = 1
                label_tensors.append(label_matrix)
            else:
                # 空标签情况
                label_tensors.append(torch.zeros(cur_batch_size, 1, device=device))
        else:
            # 单标签情况：直接转换为张量
            try:
                label_tensors.append(torch.tensor(depth_labels, device=device, dtype=torch.long))
            except:
                # 如果转换失败，创建零张量
                label_tensors.append(torch.zeros(cur_batch_size, device=device, dtype=torch.long))

    # 预计算所有相似度矩阵
    sim_scores = []
    for mask_idx in range(depth):
        cur_output = output_at_mask[:, mask_idx, :]
        sim_matrix = sim(cur_output, cur_output)
        sim_scores.append(torch.exp(sim_matrix))

    # 预处理文本内容相似度（如果提供）- 修复版本
    text_mask = None
    if text_contents is not None and len(text_contents) >= cur_batch_size:
        text_mask = torch.zeros(cur_batch_size, cur_batch_size, device=device, dtype=torch.bool)
        try:
            for i in range(cur_batch_size):
                for j in range(cur_batch_size):
                    if i != j and i < len(text_contents) and j < len(text_contents):
                        if text_contents[i] == text_contents[j]:
                            text_mask[i, j] = True
        except Exception as e:
            # 如果文本处理出错，忽略文本过滤
            print(f"Warning: Text content processing failed: {e}")
            text_mask = None

    loss_ins = torch.tensor(0.0, device=device)

    # 主循环 - 完全向量化处理（带错误处理）
    for mask_idx in range(depth):
        if mask_idx >= len(sim_scores):
            continue

        sim_score = sim_scores[mask_idx]

        for cur_depth in range(mask_idx):
            if cur_depth >= len(label_tensors):
                continue

            try:
                # 向量化构建标签关系矩阵
                label_tensor = label_tensors[cur_depth]

                if len(label_tensor.shape) == 2:  # 多标签情况
                    # 使用矩阵乘法计算标签交集：batch_size x batch_size
                    label_similarity = torch.mm(label_tensor, label_tensor.t())
                    pos_mask = label_similarity > 0
                else:  # 单标签情况
                    # 使用广播比较：batch_size x batch_size
                    pos_mask = label_tensor.unsqueeze(1) == label_tensor.unsqueeze(0)

                # 应用文本内容过滤（向量化）
                if text_mask is not None:
                    pos_mask = pos_mask & ~text_mask

                # 优化的负采样：向量化随机采样
                neg_mask = ~pos_mask
                if cur_batch_size > 4:
                    # 使用固定种子确保可重现性，但避免重复调用random.seed
                    torch.manual_seed(42 + cur_depth + mask_idx)
                    random_threshold = 0.25
                    random_mask = torch.rand(cur_batch_size, cur_batch_size, device=device) < random_threshold
                    ignore_mask = neg_mask & random_mask
                    neg_mask = neg_mask & ~ignore_mask

                # 完全向量化的损失计算
                cur_loss = compute_vectorized_loss(sim_score, pos_mask, neg_mask)

                # 累加权重损失（避免索引越界）
                if cur_depth < len(loss_weight):
                    loss_ins += cur_loss * loss_weight[cur_depth]

            except Exception as e:
                print(f"Warning: Error in contrastive loss computation at mask_idx={mask_idx}, cur_depth={cur_depth}: {e}")
                continue

    # 归一化（使用更稳定的归一化方式）
    if cur_batch_size > 0:
        loss_ins = loss_ins / (cur_batch_size * cur_batch_size)

    # 🔍 调试信息：显示最终损失值
    print(f"✅ [DEBUG] 对比损失计算完成!")
    print(f"   - 原始损失值: {loss_ins.item() * (cur_batch_size * cur_batch_size) if cur_batch_size > 0 else loss_ins.item():.6f}")
    print(f"   - 归一化后损失值: {loss_ins.item():.6f}")
    print(f"   - 损失是否为零: {'是' if loss_ins.item() == 0 else '否'}")
    print(f"   - 损失是否为NaN: {'是' if torch.isnan(loss_ins) else '否'}")

    # 清理GPU内存（可选，在内存紧张时启用）
    # torch.cuda.empty_cache() if torch.cuda.is_available() else None

    return loss_ins


def build_label_matrix_fast(cur_hier_labels, text_contents, cur_batch_size):
    """快速构建标签矩阵"""
    cur_hier_matrix = torch.zeros(cur_batch_size, cur_batch_size)

    # 简化的标签比较
    for i in range(len(cur_hier_labels)):
        label_i = cur_hier_labels[i]
        for j in range(len(cur_hier_labels)):
            # 跳过相同文本内容
            if text_contents is not None and i != j and text_contents[i] == text_contents[j]:
                cur_hier_matrix[i][j] = 0
                continue

            label_j = cur_hier_labels[j]

            # 简化的标签比较逻辑
            if isinstance(label_i, list) and isinstance(label_j, list):
                # 使用集合交集快速比较
                if set(label_i) & set(label_j):
                    cur_hier_matrix[i][j] = 1
                else:
                    cur_hier_matrix[i][j] = -1
            elif label_i == label_j:
                cur_hier_matrix[i][j] = 1
            else:
                cur_hier_matrix[i][j] = -1

    return cur_hier_matrix


def apply_simple_negative_sampling(cur_hier_matrix, cur_batch_size):
    """简化的负采样"""
    # 只进行随机负采样，跳过复杂的标签相似度过滤
    max_neg_samples = max(1, cur_batch_size // 4)

    for i in range(cur_hier_matrix.shape[0]):
        # 随机选择一些负样本设为0（忽略）
        neg_indices = random.sample(range(cur_batch_size), min(max_neg_samples, cur_batch_size))
        for idx in neg_indices:
            if cur_hier_matrix[i][idx] == -1:
                cur_hier_matrix[i][idx] = 0

    return cur_hier_matrix


def compute_loss_fast(cur_hier_matrix, sim_score):
    """快速计算损失"""
    cur_loss_ins = 0

    for i in range(cur_hier_matrix.shape[0]):
        y_true = cur_hier_matrix[i]

        # 检查是否有正样本
        pos_mask = (y_true == 1)
        if not pos_mask.any():
            continue

        cur_sim_score = sim_score[i]
        pos_sim = cur_sim_score[pos_mask].sum()
        neg_sim = cur_sim_score[y_true == 0].sum()

        # 数值稳定性检查
        if pos_sim > 0 and (pos_sim + neg_sim) > 0:
            cur_loss_ins += -torch.log(pos_sim / (pos_sim + neg_sim))

    return cur_loss_ins

def constraint_multi_depth_loss_func(logits, loss_func, hier_labels, processor, args, use_cuda=True, mode=0):
    if isinstance(logits, list):
        leaf_logits = logits[-1]
    elif isinstance(logits, torch.Tensor):
        leaf_logits = logits[:, -1, :]
    contrastive_level = 0
    hier_mapping = processor.hier_mapping
    flat_slot2value = processor.flat_slot2value
    # batch_size * label_size(134)
    depth = len(hier_mapping) + 1

    loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth - 1)]

    leaf_logits = torch.softmax(leaf_logits, dim=-1)
    hier_logits = []
    hier_logits.insert(0, leaf_logits)

    batch_s = leaf_logits.shape[0]
    constraint_loss = 0

    all_length = len(processor.all_labels)
    for depth_idx in range(depth - 2, -1, -1):
        if isinstance(logits, list):
            ori_logits = logits[depth_idx]
        elif isinstance(logits, torch.Tensor):
            ori_logits = logits[:, depth_idx, :]
        ## True
        if args.multi_verb:
            cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))

            for i in range(cur_logits.shape[-1]):
                # sum
                cur_logits[:, i] = torch.sum(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
                # mean
                # cur_logits[:, i] = torch.mean(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
        else:
            cur_logits = torch.zeros(batch_s, all_length)
            cd_labels = processor.depth2label[depth_idx]
            for i in range(all_length):
                if i in cd_labels:
                    cur_logits[:, i] = torch.sum(hier_logits[0][:, list(flat_slot2value[i])], dim=-1)
            # ver.weight.shape  [7+ 64 + 140, 768]
        cur_labels = hier_labels[depth_idx]

        if use_cuda:
            cur_logits = cur_logits.cuda()
            cur_labels = cur_labels.cuda()
        # default mode = 0
        if mode:
            cur_logits = cur_logits + ori_logits

        if args.multi_label:
            cur_multi_label = torch.zeros_like(cur_logits)
            for i in range(cur_multi_label.shape[0]):
                cur_multi_label[i][cur_labels[i]] = 1
            cur_labels = cur_multi_label

            # cur_logits = torch.softmax(cur_logits, dim=-1)
        hier_logits.insert(0, cur_logits)
        cur_constraint_loss = loss_func(cur_logits, cur_labels)
        constraint_loss += cur_constraint_loss * loss_weight[depth_idx]
    return constraint_loss


def multi_path_constraint_multi_depth_loss_func(logits, loss_func, hier_labels, processor, args, use_cuda=True, mode=0):
    contrastive_level = 0
    hier_mapping = processor.hier_mapping

    depth = len(hier_mapping) + 1

    loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth - 1)]

    batch_s = logits[0].shape[0]
    constraint_loss = 0

    for depth_idx in range(depth - 2, -1, -1):
        if isinstance(logits, list):
            pre_logits = logits[depth_idx+1]
            ori_logits = logits[depth_idx]
        elif isinstance(logits, torch.Tensor):
            pre_logits = logits[:, depth_idx+1, :]
            ori_logits = logits[:, depth_idx, :]
        else:
            print(type(logits))
            raise TypeError
        cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))

        for i in range(cur_logits.shape[-1]):
            ## sum
            # cur_logits[:, i] = torch.sum(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
            ## mean
            if len(hier_mapping[depth_idx][0][i]) != 0:
                # ori_logits[:, i] = torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1)
                # ori_logits[:, i] = torch.mean(ori_logits[:, i] + torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1), dim=-1)
                ori_logits[:, i] = ori_logits[:, i] * 0.99 + torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1) * 0.01

        cur_labels = hier_labels[depth_idx]

        if use_cuda:
            ori_logits = ori_logits.cuda()

        cur_multi_label = torch.zeros_like(cur_logits).to("cuda:0")
        for i in range(cur_multi_label.shape[0]):
            for j in cur_labels[i]:
                cur_multi_label[i][j] = 1
        cur_labels = cur_multi_label

        cur_constraint_loss = loss_func(ori_logits, cur_labels)
        constraint_loss += cur_constraint_loss * loss_weight[depth_idx]
    return constraint_loss
